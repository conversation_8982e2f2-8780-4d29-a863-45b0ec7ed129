import * as functions from "firebase-functions";
import * as admin from "firebase-admin";
import axios from "axios";

// Initialize Firebase Admin SDK
admin.initializeApp();

// Types for API responses
interface TrainLocationData {
  train_number: string;
  date: string;
  stations: Array<{
    station_code: string;
    station_name: string;
    coaches: Array<{
      coach_number: string;
      onboarding_count: number;
      off_boarding_count: number;
      vacant_count: number;
    }>;
  }>;
}

interface NotificationPayload {
  title: string;
  body: string;
  data: {
    type: string;
    station_code: string;
    coach_data: string;
    timestamp: string;
  };
}

/**
 * Get FCM token for a specific user from Firestore
 */
const getUserFcmToken = async (userId: string): Promise<string | null> => {
  try {
    const doc = await admin.firestore().collection("tokens").doc(userId).get();
    const data = doc.data();
    return data?.fcm_token || null;
  } catch (error) {
    console.error(`Error fetching FCM token for user ${userId}:`, error);
    return null;
  }
};

/**
 * Check if alert has already been sent to prevent duplicates
 */
const isAlertAlreadySent = async (alertKey: string): Promise<boolean> => {
  try {
    const doc = await admin.firestore().collection("sentAlerts").doc(alertKey).get();
    return doc.exists;
  } catch (error) {
    console.error(`Error checking sent alerts for ${alertKey}:`, error);
    return false;
  }
};

/**
 * Mark alert as sent in Firestore
 */
const markAlertAsSent = async (alertKey: string): Promise<void> => {
  try {
    await admin.firestore().collection("sentAlerts").doc(alertKey).set({
      timestamp: admin.firestore.FieldValue.serverTimestamp(),
      sent_at: new Date().toISOString(),
    });
  } catch (error) {
    console.error(`Error marking alert as sent for ${alertKey}:`, error);
  }
};

/**
 * Fetch train location data from microservice
 */
const fetchTrainLocationData = async (
  trainNumber: string,
  date: string,
  bearerToken: string
): Promise<TrainLocationData | null> => {
  try {
    const response = await axios.get(
      "https://railops-uat-api.biputri.com/microservice/train/location/",
      {
        params: {
          train_number: trainNumber,
          date: date,
        },
        headers: {
          "Authorization": `Bearer ${bearerToken}`,
          "Content-Type": "application/json",
        },
        timeout: 10000, // 10 second timeout
      }
    );

    // Log the actual API response for debugging
    console.log("Train location API response:", JSON.stringify(response.data, null, 2));

    // Validate response structure
    const data = response.data;
    if (!data || typeof data !== "object") {
      console.error("Invalid API response: data is not an object");
      return null;
    }

    // Transform the API response to match our expected format
    // API returns: { "BWN": { "D3": [], "S7": [] }, "NJP": { "C1": [] } }
    // We need: { stations: [{ station_code: "BWN", coaches: [...] }] }

    const transformedData: TrainLocationData = {
      train_number: trainNumber,
      date: date,
      stations: []
    };

    // Convert station data to our expected format
    for (const [stationCode, coachData] of Object.entries(data)) {
      if (typeof coachData === "object" && coachData !== null) {
        const coaches = [];

        // Convert coach data to our expected format
        for (const [coachNumber, passengerData] of Object.entries(coachData)) {
          if (Array.isArray(passengerData)) {
            coaches.push({
              coach_number: coachNumber,
              onboarding_count: passengerData.length > 0 ? passengerData[0] || 0 : 0,
              off_boarding_count: passengerData.length > 1 ? passengerData[1] || 0 : 0,
              vacant_count: passengerData.length > 2 ? passengerData[2] || 0 : 0,
            });
          } else {
            // Handle case where coach data is not an array
            coaches.push({
              coach_number: coachNumber,
              onboarding_count: 0,
              off_boarding_count: 0,
              vacant_count: 0,
            });
          }
        }

        transformedData.stations.push({
          station_code: stationCode,
          station_name: stationCode, // Use station code as name for now
          coaches: coaches
        });
      }
    }

    console.log("Transformed data:", JSON.stringify(transformedData, null, 2));
    return transformedData;
  } catch (error) {
    console.error("Error fetching train location data:", error);
    return null;
  }
};

/**
 * Build coach table string for notification body (optimized for FCM size limits)
 */
const buildCoachTable = (stations: TrainLocationData["stations"]): string => {
  // Validate stations array
  if (!Array.isArray(stations)) {
    console.error("buildCoachTable: stations is not an array", stations);
    return "No station data available";
  }

  if (stations.length === 0) {
    console.log("buildCoachTable: stations array is empty");
    return "No stations found";
  }

  // Create a concise summary instead of a full table to avoid FCM size limits
  let summary = "";
  let totalCoaches = 0;
  let totalOnboarding = 0;
  let totalOffBoarding = 0;
  let totalVacant = 0;

  stations.forEach((station) => {
    // Validate station object
    if (!station || typeof station !== "object") {
      console.error("buildCoachTable: invalid station object", station);
      return;
    }

    // Validate station has required properties
    if (!station.station_code) {
      console.error("buildCoachTable: station missing station_code", station);
      return;
    }

    // Validate coaches array
    if (!Array.isArray(station.coaches)) {
      console.error("buildCoachTable: station.coaches is not an array", station);
      return;
    }

    if (station.coaches.length === 0) {
      console.log(`buildCoachTable: no coaches found for station ${station.station_code}`);
      return;
    }

    let stationOnboarding = 0;
    let stationOffBoarding = 0;
    let stationVacant = 0;

    station.coaches.forEach((coach) => {
      // Validate coach object
      if (!coach || typeof coach !== "object") {
        console.error("buildCoachTable: invalid coach object", coach);
        return;
      }

      // Use default values for missing properties
      const onboardingCount = coach.onboarding_count || 0;
      const offBoardingCount = coach.off_boarding_count || 0;
      const vacantCount = coach.vacant_count || 0;

      stationOnboarding += onboardingCount;
      stationOffBoarding += offBoardingCount;
      stationVacant += vacantCount;
      totalCoaches++;
    });

    totalOnboarding += stationOnboarding;
    totalOffBoarding += stationOffBoarding;
    totalVacant += stationVacant;

    // Add station summary (only if there's activity)
    if (stationOnboarding > 0 || stationOffBoarding > 0 || stationVacant > 0) {
      summary += `${station.station_code}: ${stationOnboarding}/${stationOffBoarding}/${stationVacant}\n`;
    }
  });

  // Create concise summary
  const result = `Total: ${totalOnboarding}/${totalOffBoarding}/${totalVacant} (${totalCoaches} coaches)\n${summary}`;

  // Ensure the result is not too long for FCM
  if (result.length > 200) {
    return `Total: ${totalOnboarding}/${totalOffBoarding}/${totalVacant} across ${totalCoaches} coaches`;
  }

  return result;
};

/**
 * Send FCM notification with custom sound
 */
const sendFcmNotification = async (
  fcmToken: string,
  payload: NotificationPayload
): Promise<boolean> => {
  try {
    const message = {
      token: fcmToken,
      notification: {
        title: payload.title,
        body: payload.body,
      },
      data: payload.data,
      android: {
        notification: {
          channelId: "railops_alerts",
          sound: "railops_alarm",
          priority: "high" as const,
        },
      },
      apns: {
        payload: {
          aps: {
            sound: "railops_alarm.caf",
            category: "RAILOPS_ALERT",
          },
        },
      },
    };

    const response = await admin.messaging().send(message);
    console.log("FCM message sent successfully:", response);
    return true;
  } catch (error) {
    console.error("Error sending FCM message:", error);
    return false;
  }
};

/**
 * Main Cloud Function: POST /notify endpoint
 * Fetches train location data, builds coach tables, and sends FCM notifications
 */
export const notify = functions.https.onRequest(async (req, res) => {
  // Enable CORS
  res.set("Access-Control-Allow-Origin", "*");
  res.set("Access-Control-Allow-Methods", "POST, OPTIONS");
  res.set("Access-Control-Allow-Headers", "Content-Type, Authorization");

  if (req.method === "OPTIONS") {
    res.status(200).send();
    return;
  }

  if (req.method !== "POST") {
    res.status(405).json({ error: "Method not allowed. Use POST." });
    return;
  }

  try {
    // Extract request parameters
    const { user_id, train_number, date, lat, lng } = req.body;

    // Validate required parameters
    if (!user_id || !train_number || !date) {
      res.status(400).json({
        error: "Missing required parameters: user_id, train_number, date",
      });
      return;
    }

    // Log coordinates if provided (for future proximity-based features)
    if (lat && lng) {
      console.log(`Request coordinates: lat=${lat}, lng=${lng}`);
    }

    console.log(`Processing notification request for user: ${user_id}, train: ${train_number}, date: ${date}`);

    // Log invocation details for monitoring
    console.log(`[NOTIFY] Invocation - User: ${user_id}, Train: ${train_number}, Date: ${date}, Coordinates: ${lat || "N/A"},${lng || "N/A"}`);

    // Get RAILOPS_BEARER secret
    const bearerToken = functions.config().railops?.bearer;
    if (!bearerToken) {
      console.error("RAILOPS_BEARER secret not configured");
      res.status(500).json({ error: "Server configuration error" });
      return;
    }

    // Check for duplicate alerts
    const alertKey = `${user_id}/${date}/${train_number}`;
    const alreadySent = await isAlertAlreadySent(alertKey);
    if (alreadySent) {
      console.log(`[NOTIFY] Status: SKIPPED, User: ${user_id}, Station: N/A - Alert already sent for ${alertKey}`);
      res.status(200).json({ message: "Alert already sent", skipped: true });
      return;
    }

    // Get user's FCM token
    const fcmToken = await getUserFcmToken(user_id);
    if (!fcmToken) {
      console.log(`[NOTIFY] Status: SKIPPED, User: ${user_id}, Station: N/A - No FCM token found`);
      res.status(404).json({ error: "User FCM token not found" });
      return;
    }

    // Fetch train location data
    const trainData = await fetchTrainLocationData(train_number, date, bearerToken);
    if (!trainData) {
      console.log(`[NOTIFY] Status: ERROR, User: ${user_id}, Station: N/A - Failed to fetch train location data for ${train_number}`);
      res.status(500).json({ error: "Failed to fetch train location data" });
      return;
    }

    // Build coach table for notification
    const coachTable = buildCoachTable(trainData.stations);

    // Create notification payload (optimized for FCM size limits)
    const notificationPayload: NotificationPayload = {
      title: `Train ${train_number} - Coach Updates`,
      body: coachTable,
      data: {
        type: "train_location_update",
        station_code: trainData.stations[0]?.station_code || "",
        coach_data: JSON.stringify({
          stations_count: trainData.stations.length,
          summary: coachTable
        }), // Reduced payload size
        timestamp: new Date().toISOString(),
      },
    };

    // Send FCM notification
    const notificationSent = await sendFcmNotification(fcmToken, notificationPayload);
    if (!notificationSent) {
      console.log(`[NOTIFY] Status: ERROR, User: ${user_id}, Station: ${trainData.stations[0]?.station_code || "N/A"} - Failed to send FCM notification`);
      res.status(500).json({ error: "Failed to send notification" });
      return;
    }

    // Mark alert as sent to prevent duplicates
    await markAlertAsSent(alertKey);

    const stationCode = trainData.stations[0]?.station_code || "N/A";
    console.log(`[NOTIFY] Status: SENT, User: ${user_id}, Station: ${stationCode} - Notification sent successfully`);
    res.status(200).json({
      message: "Notification sent successfully",
      user_id: user_id,
      train_number: train_number,
      stations_count: trainData.stations.length,
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error("Error in notify function:", error);
    res.status(500).json({
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});
