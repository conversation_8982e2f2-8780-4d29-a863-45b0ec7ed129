class Routes {
  static const String splashScreen = '/';
  static const String login = '/login';
  static const String home = '/home';
  static const String signUp = '/signup';
  static const String requestUser = '/request-user-managemnet';
  static const String otpEnter = '/otp-enter';
  static const String mobileLogin = '/mobile-login';
  static const String forgotPassword = '/forgot-password';
  static const String TrainDetails = '/train-details';
  static const String editProfle = '/edit_profile';
  static const String changeEmail = '/change_mail';
  static const String changeMobile = '/change_mobile';
  static const String changeWhatsapp = '/change_whatsapp';
  static const String changePassword = '/change_password';
  static const String addTrainProfile = '/add-train-profile';
  static const String mapScreen = '/map-screen';
  static const String uploadData = '/upload-data';
  static const String AssignEhkCa = '/assign-ehk-ca';
  static const String editTrain = '/edit-train';
  static const String addTrain = '/add-train-details';
  static const String attendance = '/attendance';
  static const String passengerFeedbackScreen = '/passenger-feedback';
  static const String assignObhs = '/assign-obhs';
  static const String pdfScreen = '/pdf-screen';
  static const String userInfo = '/user-management';
  static const String addUser = '/add-user';
  static const String updateUser = '/update-user';
  static const String requestedContractorUser = '/requested-contractor-user';
  static const String tripReport = '/trip-report';
  static const String issueScrren = '/issue-screen';
  static const String pnrScrren = '/pnr-screen';
  static const String railSathiQr = '/rail-sathi-qr';
  static const String customerCare = '/customer-care';
  static const String railSathi = '/rail-sathi';
  static const String notificationCenter = '/notification-center';
  static const String notificationSettings = '/notification-settings';
  static const String notificationTesting = '/notification-testing';
  static const String notificationSystemTest = '/notification-system-test';
}
